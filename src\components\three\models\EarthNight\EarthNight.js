import * as THREE from "three";
import { EARTH_FRAGMENTS, EARTH_RADIUS, PATHS } from "../../../../constants";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";
import { texturePreloader } from "../../utils/TexturePreloader.js";
import { GUI } from "lil-gui";

class EarthNight {
  constructor(scene, { renderer, enableGUI = true } = {}) {
    this.scene = scene;
    this.renderer = renderer;
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {
      day: null,
      night: null,
    };
    this.uniforms = null;
    this.lightPos = new THREE.Vector3().setScalar(2);

    // GUI相关属性
    this.enableGUI = enableGUI;
    this.gui = null;
    this.folder = null;
    this.nightParams = {
      brightness: 1.0,
      contrast: 1.03,
      gamma: 0.87, // 伽马校正，<1.0让亮处更亮暗处更暗，>1.0相反
      redMultiplier: 0.85, // 红色通道倍数
      greenMultiplier: 0.85, // 绿色通道倍数
      blueMultiplier: 1.0, // 蓝色通道倍数
    };

    this.init();
  }

  async init() {
    await this.loadTextures();
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();

    // 初始化GUI控制面板
    if (this.enableGUI) {
      this.initGUI();
    }
  }

  async loadTextures() {
    try {
      // 优先使用预加载的纹理
      const preloadedTextures = texturePreloader.getAllTextures();

      if (preloadedTextures.earthMap && preloadedTextures.earthNight) {
        console.log("EarthNight: 使用预加载的纹理");

        this.textures.day = preloadedTextures.earthMap;
        this.textures.night = preloadedTextures.earthNight;
      } else {
        console.log("EarthNight: 预加载纹理不可用，使用异步加载");

        // 如果预加载纹理不可用，使用传统的异步加载方式
        const textureLoader = new THREE.TextureLoader();
        const [dayTexture, nightTexture] = await Promise.all([this.loadTexture(textureLoader, PATHS.earthMap), this.loadTexture(textureLoader, PATHS.earthNight)]);

        this.textures.day = dayTexture;
        this.textures.night = nightTexture;
      }

      // Set color space for proper color rendering
      // this.textures.day.colorSpace = THREE.SRGBColorSpace; // Color texture needs sRGB
      // this.textures.night.colorSpace = THREE.SRGBColorSpace; // Color texture needs sRGB

      // Set anisotropy for better quality
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      this.textures.day.anisotropy = maxAnisotropy;
      this.textures.night.anisotropy = maxAnisotropy;

      console.log("EarthNight: 纹理加载完成");
    } catch (error) {
      console.error("Error loading EarthNight textures:", error);
    }
  }

  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  createGeometry() {
    this.geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS);
  }

  createMaterial() {
    this.uniforms = {
      uDay: { value: this.textures.day },
      uNight: { value: this.textures.night },
      uLight: { value: this.lightPos },
      uNightBrightness: { value: this.nightParams.brightness }, // 亮度调整
      uNightContrast: { value: this.nightParams.contrast }, // 对比度调整
      uNightGamma: { value: this.nightParams.gamma }, // 伽马校正
      uNightRGBMultiplier: { value: new THREE.Vector3(this.nightParams.redMultiplier, this.nightParams.greenMultiplier, this.nightParams.blueMultiplier) }, // RGB通道调整
    };

    this.material = new CustomShaderMaterial({
      baseMaterial: THREE.MeshBasicMaterial,
      vertexShader: `
        uniform vec3 uLight;
        varying vec2 vUv2;
        varying float vDist;

        void main() {
          vUv2 = uv;

          // 将顶点位置转换到世界坐标系
          vec4 worldPosition213 = modelMatrix * vec4(position, 1.0);
          vec3 worldNormal = normalize(worldPosition213.xyz);

          // 计算从顶点到光源的方向
          vec3 lightDirection = normalize(uLight - worldPosition213.xyz);

          // 计算点积，用于确定光照强度
          float dotProduct = dot(worldNormal, lightDirection);

          // 直接使用点积值作为过渡参数
          vDist = dotProduct;
        }
      `,
      fragmentShader: `
        uniform sampler2D uNight;
        uniform vec3 uLight;
        uniform float uNightBrightness;
        uniform float uNightContrast;
        uniform float uNightGamma;
        uniform vec3 uNightRGBMultiplier;
        varying vec2 vUv2;
        varying float vDist;

        void main() {
          vec4 texNight = texture2D(uNight, vUv2);

          // 应用亮度和对比度调整
          // 对比度调整：(color - 0.5) * contrast + 0.5
          // 亮度调整：color * brightness
          texNight.rgb = ((texNight.rgb - 0.5) * uNightContrast + 0.5) * uNightBrightness;

          // 应用伽马校正 - 让亮的地方更亮，暗的地方更暗
          // gamma < 1.0: 增强对比度，亮处更亮，暗处更暗
          // gamma > 1.0: 减少对比度，整体变亮
          texNight.rgb = pow(max(texNight.rgb, 0.0), vec3(1.0 / uNightGamma));

          // 应用RGB通道调整
          // 分别调整红、绿、蓝三个颜色通道
          texNight.rgb *= uNightRGBMultiplier;

          vec4 clear = vec4(0, 0, 0, 0);

          // vDist是光照强度 [-1, 1]
          // vDist > 0: 面向光源（白天区域）
          // vDist < 0: 背离光源（黑夜区域）
          // vDist = 0: 昼夜分界线

          // 创建平滑的过渡，让昼夜各占一半
          // 使用更宽的过渡区域来模拟大气散射效果
          float fadeOut = smoothstep(-0.1, 0.1, vDist);
          vec4 d = mix(texNight, clear, fadeOut);

          csm_DiffuseColor = d;
        }
      `,
      uniforms: this.uniforms,
      transparent: true,
    });
  }

  createMesh() {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.receiveShadow = true;
    const scale = 1.001;
    this.mesh.scale.set(scale, scale, scale);
    this.mesh.rotation.y = Math.PI;
    this.mesh.renderOrder = 0; // 与地球白天层相同的渲染顺序
  }

  addToScene() {
    if (this.scene && this.mesh) {
      // this.mesh.visible = false;
      this.scene.add(this.mesh);
    }
  }

  update(lightPos) {
    if (this.material && this.material.uniforms && lightPos) {
      this.material.uniforms.uLight.value = lightPos;
    }
  }

  /**
   * 设置夜晚纹理的亮度
   * @param {number} brightness - 亮度值，1.0为默认值，>1.0更亮，<1.0更暗
   */
  setNightBrightness(brightness) {
    this.nightParams.brightness = brightness;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightBrightness) {
      this.material.uniforms.uNightBrightness.value = brightness;
    }
  }

  /**
   * 设置夜晚纹理的对比度
   * @param {number} contrast - 对比度值，1.0为默认值，>1.0对比度更高，<1.0对比度更低
   */
  setNightContrast(contrast) {
    this.nightParams.contrast = contrast;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightContrast) {
      this.material.uniforms.uNightContrast.value = contrast;
    }
  }

  /**
   * 设置夜晚纹理的伽马校正
   * @param {number} gamma - 伽马值，1.0为默认值，<1.0让亮处更亮暗处更暗，>1.0相反
   */
  setNightGamma(gamma) {
    this.nightParams.gamma = gamma;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightGamma) {
      this.material.uniforms.uNightGamma.value = gamma;
    }
  }

  /**
   * 设置夜晚纹理的RGB通道调整
   * @param {number} red - 红色通道倍数，1.0为默认值
   * @param {number} green - 绿色通道倍数，1.0为默认值
   * @param {number} blue - 蓝色通道倍数，1.0为默认值
   */
  setNightRGB(red, green, blue) {
    this.nightParams.redMultiplier = red;
    this.nightParams.greenMultiplier = green;
    this.nightParams.blueMultiplier = blue;

    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.set(red, green, blue);
    }
  }

  /**
   * 设置夜晚纹理的红色通道
   * @param {number} red - 红色通道倍数
   */
  setNightRed(red) {
    this.nightParams.redMultiplier = red;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.x = red;
    }
  }

  /**
   * 设置夜晚纹理的绿色通道
   * @param {number} green - 绿色通道倍数
   */
  setNightGreen(green) {
    this.nightParams.greenMultiplier = green;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.y = green;
    }
  }

  /**
   * 设置夜晚纹理的蓝色通道
   * @param {number} blue - 蓝色通道倍数
   */
  setNightBlue(blue) {
    this.nightParams.blueMultiplier = blue;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.z = blue;
    }
  }

  /**
   * 同时设置夜晚纹理的所有参数
   * @param {number} brightness - 亮度值
   * @param {number} contrast - 对比度值
   * @param {number} gamma - 伽马值（可选）
   * @param {Object} rgb - RGB调整对象 {r, g, b}（可选）
   */
  setNightAppearance(brightness, contrast, gamma = null, rgb = null) {
    this.setNightBrightness(brightness);
    this.setNightContrast(contrast);
    if (gamma !== null) {
      this.setNightGamma(gamma);
    }
    if (rgb !== null) {
      this.setNightRGB(rgb.r || 1.0, rgb.g || 1.0, rgb.b || 1.0);
    }
  }

  /**
   * 获取当前夜晚纹理的亮度值
   * @returns {number} 当前亮度值
   */
  getNightBrightness() {
    return this.material?.uniforms?.uNightBrightness?.value || 1.0;
  }

  /**
   * 获取当前夜晚纹理的对比度值
   * @returns {number} 当前对比度值
   */
  getNightContrast() {
    return this.material?.uniforms?.uNightContrast?.value || 1.0;
  }

  /**
   * 获取当前夜晚纹理的伽马值
   * @returns {number} 当前伽马值
   */
  getNightGamma() {
    return this.material?.uniforms?.uNightGamma?.value || 1.0;
  }

  /**
   * 获取当前夜晚纹理的RGB调整值
   * @returns {Object} RGB调整值 {r, g, b}
   */
  getNightRGB() {
    const rgb = this.material?.uniforms?.uNightRGBMultiplier?.value;
    return rgb ? { r: rgb.x, g: rgb.y, b: rgb.z } : { r: 1.0, g: 1.0, b: 1.0 };
  }

  /**
   * 初始化GUI控制面板
   */
  initGUI() {
    if (!this.enableGUI) {
      return;
    }

    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建夜晚纹理控制文件夹
      this.folder = this.gui.addFolder("🌙 夜晚纹理 (Night Texture)");
      this.folder.open();

      this.setupGUIControls();
    } catch (error) {
      console.warn("EarthNight GUI setup warning:", error);
    }
  }

  /**
   * 设置GUI控制项
   */
  setupGUIControls() {
    if (!this.folder) return;

    // 亮度控制
    this.folder
      .add(this.nightParams, "brightness", 0.1, 3.0, 0.1)
      .name("🔆 亮度 (Brightness)")
      .onChange((value) => {
        this.setNightBrightness(value);
      });

    // 对比度控制
    this.folder
      .add(this.nightParams, "contrast", 0.1, 3, 0.01)
      .name("🎭 对比度 (Contrast)")
      .onChange((value) => {
        this.setNightContrast(value);
      });

    // 伽马校正控制
    this.folder
      .add(this.nightParams, "gamma", 0.3, 2.5, 0.01)
      .name("⚡ 伽马校正 (Gamma)")
      .onChange((value) => {
        this.setNightGamma(value);
      });

    // 创建RGB调整子文件夹
    const rgbFolder = this.folder.addFolder("🎨 RGB通道调整 (RGB Channels)");
    rgbFolder.open();

    // 红色通道控制
    rgbFolder
      .add(this.nightParams, "redMultiplier", 0.0, 2.0, 0.01)
      .name("🔴 红色 (Red)")
      .onChange((value) => {
        this.setNightRed(value);
      });

    // 绿色通道控制
    rgbFolder
      .add(this.nightParams, "greenMultiplier", 0.0, 2.0, 0.01)
      .name("🟢 绿色 (Green)")
      .onChange((value) => {
        this.setNightGreen(value);
      });

    // 蓝色通道控制
    rgbFolder
      .add(this.nightParams, "blueMultiplier", 0.0, 2.0, 0.01)
      .name("🔵 蓝色 (Blue)")
      .onChange((value) => {
        this.setNightBlue(value);
      });

    // 重置按钮
    const resetControls = {
      reset: () => {
        this.nightParams.brightness = 1.0;
        this.nightParams.contrast = 1.0;
        this.nightParams.gamma = 1.0;
        this.nightParams.redMultiplier = 1.0;
        this.nightParams.greenMultiplier = 1.0;
        this.nightParams.blueMultiplier = 1.0;
        this.setNightBrightness(1.0);
        this.setNightContrast(1.0);
        this.setNightGamma(1.0);
        this.setNightRGB(1.0, 1.0, 1.0);
        // 刷新GUI显示
        this.folder.controllersRecursive().forEach((controller) => {
          controller.updateDisplay();
        });
      },
    };

    this.folder.add(resetControls, "reset").name("🔄 重置默认值");
  }

  destroy() {
    // 清理GUI
    if (this.folder && this.gui) {
      try {
        this.gui.removeFolder(this.folder);
      } catch (error) {
        console.warn("Error removing GUI folder:", error);
      }
    }

    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Dispose of textures
    Object.values(this.textures).forEach((texture) => {
      if (texture) {
        texture.dispose();
      }
    });

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {};
    this.uniforms = null;
    this.gui = null;
    this.folder = null;
  }
}

// Export both named and default exports for flexibility
export { EarthNight };
export default EarthNight;
