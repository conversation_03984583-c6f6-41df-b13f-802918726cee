<template>
  <div class="Qx3">
    <div ref="chartRef2" class="chart-container"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

const chartRef2 = ref(null);
let myChart = null;

// 颜色配置
const colorConfig = {
  今天: "#FD9391", // 红色
  昨天: "#97C3FD", // 蓝色
  平均: "#32FEFC", // 绿色
};

// 计算属性：从 dataStore 获取动态数据
const chartData = computed(() => {
  const warningHour = dataStore.data?.warningHour;
  const warningHourItem = dataStore.data?.warningHourItem;

  if (!warningHour || !warningHourItem) {
    // 如果数据还未加载，返回默认值
    return {
      xAxisData: Array.from({ length: 24 }, (_, i) => i.toString()),
      currentData: Array(24).fill(0),
      afterData: Array(24).fill(0),
      avgData: Array(24).fill(0),
    };
  }

  return {
    xAxisData: warningHourItem || [],
    currentData: warningHour.current || [],
    afterData: warningHour.after || [],
    avgData: warningHour.avg || [],
  };
});

const initChart = () => {
  if (chartRef2.value) {
    myChart = echarts.init(chartRef2.value);
    updateChart();

    // 响应式处理
    window.addEventListener("resize", () => {
      myChart && myChart.resize();
    });
  }
};

const updateChart = () => {
  if (!myChart) return;

  const option = {
    backgroundColor: "transparent",
    legend: {
      data: [
        { name: "今天", itemStyle: { color: colorConfig.今天 } },
        { name: "昨天", itemStyle: { color: colorConfig.昨天 } },
        { name: "平均", itemStyle: { color: colorConfig.平均 } },
      ],
      right: "10%",
      top: "5%",
      textStyle: {
        color: "#ffffff",
        fontSize: 12,
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 15,
      //圆圈
      icon: "circle",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.value.xAxisData,
      axisLine: {
        lineStyle: {
          color: "#00d4ff",
        },
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: "今天",
        data: chartData.value.currentData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.今天,
          width: 2,
        },
      },
      {
        name: "昨天",
        data: chartData.value.afterData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.昨天,
          width: 2,
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: colorConfig.昨天 + "66", // 顶部颜色，40%透明度
              },
              {
                offset: 1,
                color: colorConfig.昨天 + "0D", // 底部颜色，5%透明度
              },
            ],
          },
        },
      },
      {
        name: "平均",
        data: chartData.value.avgData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.平均,
          width: 2,
        },
      },
    ],
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#00d4ff",
      textStyle: {
        color: "#ffffff",
      },
    },
  };

  myChart.setOption(option);
};

// 监听数据变化，自动更新图表
watch(
  chartData,
  () => {
    updateChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", () => {
    myChart && myChart.resize();
  });
});
</script>

<style lang="less" scoped>
.Qx3 {
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
