<template>
  <div v-if="visible" class="attack-list" :style="{ animationDelay: animationDelay }">
    <div class="list-container">
      <div class="list-content">
        <div class="list-item" v-for="(item, index) in data" :key="`${item.id}-${index}`">
          <slot :item="item" :index="index">
            <!-- 默认插槽内容，如果没有提供插槽则显示 -->
            <div class="default-item">{{ item }}</div>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props 定义
const props = defineProps({
  // 是否显示组件
  visible: {
    type: Boolean,
    default: true,
  },
  // 动画延迟
  animationDelay: {
    type: String,
    default: "0s",
  },
  // 数据列表
  data: {
    type: Array,
    default: () => [],
  },
});
</script>

<style lang="less" scoped>
.attack-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: all;

  // 继承父组件的动画样式
  animation: slideInFromRight var(--animation-duration, 1s) ease-out forwards;
  transform: translateX(100%);
  opacity: 0;
}

.list-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.list-content {
  width: 100%;
}

.list-item {
  width: 100%;
  display: block;
  box-sizing: border-box;
}

.default-item {
  padding: 10px;
  border: 1px solid #ccc;
  margin-bottom: 10px;
  background: #f9f9f9;
}

/* 从右到左滑动动画样式 */
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
