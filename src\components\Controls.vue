<template>
  <div class="controls-outer">
    <!-- 时间显示 -->
    <div class="current-time">
      <div>{{ formatChinaTime(props.time) }}</div>
    </div>

    <!-- 时间轴控制器 -->
    <div class="time-axis-container" @mousedown="handleMouseDown">
      <!-- 时间轴背景 -->
      <div class="time-axis-background"></div>

      <!-- 时间刻度线容器 -->
      <div class="ticker-container" :style="{ transform: `translateX(${finalTickerOffset % PX_PER_DAY}px)` }">
        <!-- 生成三个重复的刻度线组以实现无缝滚动 -->
        <div v-for="setIndex in [-1, 0, 1]" :key="setIndex" class="ticker-set" :style="{ transform: `translateX(${setIndex * PX_PER_DAY}px)` }">
          <!-- 时间刻度线 -->
          <div class="ticker-lines">
            <div v-for="tickIndex in TOTAL_TICKS" :key="tickIndex" :class="getTickClass(tickIndex - 1)" :style="{ left: `${((tickIndex - 1) / TOTAL_TICKS) * PX_PER_DAY}px` }">
              <!-- 时间标签 -->
              <div v-if="shouldShowTimeLabel(tickIndex - 1)" class="time-label">
                {{ getTimeLabel(tickIndex - 1) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中心指示器 -->
      <div class="center-indicator"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { msToPx, pxToMs } from "../utils";
import { PX_PER_DAY, MS_PER_DAY } from "../constants";

// 常量定义
const TOTAL_TICKS = 144; // 24小时 * 6个刻度/小时 = 144个刻度

const props = defineProps({
  prevOffsetMsRef: {
    type: Object,
    required: true,
  },
  time: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits(["change-offset"]);

// 响应式数据
const tickerOffset = ref(0);
const isDragging = ref(false);
const startX = ref(0);

// 计算当前时间在一天中的偏移量
const currentTimeOffset = computed(() => {
  const date = new Date(props.time);
  const msInDay = date.getHours() * 60 * 60 * 1000 + date.getMinutes() * 60 * 1000 + date.getSeconds() * 1000 + date.getMilliseconds();

  // 将时间偏移转换为像素偏移，并反向（因为刻度线需要向左移动）
  // 这样当前时间会对齐到中心指示器
  return -msToPx(msInDay);
});

// 计算最终的刻度线偏移
const finalTickerOffset = computed(() => {
  return currentTimeOffset.value + tickerOffset.value;
});

// 格式化中国时间显示
const formatChinaTime = (timestamp) => {
  const date = new Date(timestamp);
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${hours}:${minutes}:${seconds}`;
};

// 获取刻度线的CSS类
const getTickClass = (tickIndex) => {
  const hour = Math.floor(tickIndex / 6); // 每小时6个刻度
  const minuteInHour = (tickIndex % 6) * 10; // 每个刻度10分钟

  // 00:00 午夜特殊标记
  if (hour === 0 && minuteInHour === 0) {
    return "tick midnight-tick";
  }

  // 12:00 正午特殊标记
  if (hour === 12 && minuteInHour === 0) {
    return "tick noon-tick";
  }

  // 06:00 和 18:00 特殊标记
  if ((hour === 6 || hour === 18) && minuteInHour === 0) {
    return "tick quarter-day-tick";
  }

  // 整点标记
  if (minuteInHour === 0) {
    return "tick hour-tick";
  }

  // 半小时标记
  if (minuteInHour === 30) {
    return "tick half-hour-tick";
  }

  // 普通刻度
  return "tick normal-tick";
};

// 是否显示时间标签
const shouldShowTimeLabel = (tickIndex) => {
  const minuteInHour = (tickIndex % 6) * 10;
  const hour = Math.floor(tickIndex / 6);

  // 只在整点显示标签
  if (minuteInHour !== 0) return false;

  // 显示关键时间点：00:00, 06:00, 12:00, 18:00 和每4小时的整点
  return hour % 4 === 0 || hour === 6 || hour === 18;
};

// 获取时间标签文本（24小时制）
const getTimeLabel = (tickIndex) => {
  const hour = Math.floor(tickIndex / 6);
  return `${String(hour).padStart(2, "0")}:00`;
};

// 鼠标按下事件处理
const handleMouseDown = (event) => {
  isDragging.value = true;
  startX.value = event.clientX;
  event.preventDefault();

  // 添加全局事件监听器
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};

// 鼠标移动事件处理
const handleMouseMove = (event) => {
  if (!isDragging.value) return;

  const deltaX = event.clientX - startX.value;
  const deltaMs = pxToMs(deltaX);
  const totalMs = deltaMs + props.prevOffsetMsRef.current;
  const deltaPx = msToPx(totalMs);

  tickerOffset.value = deltaPx;
  emit("change-offset", -totalMs);
};

// 鼠标释放事件处理
const handleMouseUp = () => {
  if (!isDragging.value) return;

  isDragging.value = false;
  const deltaMs = pxToMs(tickerOffset.value);
  props.prevOffsetMsRef.current = props.prevOffsetMsRef.current + deltaMs;

  // 移除全局事件监听器
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
};
</script>

<style scoped>
.controls-outer {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* 时间显示样式 */
.current-time {
  position: absolute;
  bottom: 48px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 48px;
  font-weight: 400;
  color: white;
  text-align: center;
  pointer-events: none;
  user-select: none;
  z-index: 20;
  font-variant-numeric: tabular-nums;
}

.current-time div {
  line-height: 1;
  font-family: "Courier New", monospace;
  letter-spacing: 0.05em;
}

/* 时间轴容器样式 */
.time-axis-container {
  position: relative;
  width: 100%;
  height: 60px;
  cursor: grab;
  user-select: none;
  overflow: hidden;
}

.time-axis-container:active {
  cursor: grabbing;
}

.time-axis-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.05) 100%);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 刻度线容器样式 */
.ticker-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.ticker-set {
  position: absolute;
  width: 1400px; /* PX_PER_DAY */
  height: 100%;
  top: 0;
}

.ticker-lines {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 刻度线基础样式 */
.tick {
  position: absolute;
  bottom: 0;
  width: 1px;
  background-color: #606a9c;
  transform-origin: bottom;
}

/* 普通刻度 (每10分钟) */
.normal-tick {
  height: 8px;
  opacity: 0.3;
  background-color: #606a9c;
}

/* 半小时刻度 (每30分钟) */
.half-hour-tick {
  height: 15px;
  opacity: 0.6;
  background-color: #7080bc;
  width: 1.5px;
}

/* 整点刻度 */
.hour-tick {
  height: 22px;
  opacity: 0.8;
  background-color: #8090cc;
  width: 1.5px;
}

/* 正午刻度 (12:00) */
.noon-tick {
  height: 35px;
  background-color: #ffaa00;
  opacity: 1;
  box-shadow: 0 0 6px rgba(255, 170, 0, 0.8);
  width: 2px;
}

/* 午夜刻度 (00:00) */
.midnight-tick {
  height: 35px;
  background-color: #ffffff;
  opacity: 1;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
  width: 2px;
}

/* 06:00 和 18:00 特殊刻度 */
.quarter-day-tick {
  height: 28px;
  background-color: #66aaff;
  opacity: 1;
  box-shadow: 0 0 4px rgba(102, 170, 255, 0.6);
  width: 2px;
}

/* 时间标签样式 */
.time-label {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  pointer-events: none;
  user-select: none;
  background: rgba(0, 0, 0, 0.4);
  padding: 2px 6px;
  border-radius: 3px;
  backdrop-filter: blur(2px);
  font-family: "Courier New", monospace;
}

/* 特殊时间点的标签样式 */
.midnight-tick .time-label {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: 600;
}

.noon-tick .time-label {
  color: #ffaa00;
  background: rgba(255, 170, 0, 0.15);
  border: 1px solid rgba(255, 170, 0, 0.4);
  font-weight: 600;
}

.quarter-day-tick .time-label {
  color: #66aaff;
  background: rgba(102, 170, 255, 0.15);
  border: 1px solid rgba(102, 170, 255, 0.4);
  font-weight: 600;
}

/* 中心指示器样式 */
.center-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.8) 20%, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0.8) 80%, transparent 100%);
  border-radius: 1px;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
  pointer-events: none;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .current-time {
    font-size: 36px;
    bottom: 36px;
  }

  .time-axis-container {
    height: 50px;
  }

  .time-label {
    font-size: 8px;
    bottom: 28px;
  }
}
</style>
