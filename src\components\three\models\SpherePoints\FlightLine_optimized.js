import * as THREE from "three";
import { EARTH_RADIUS } from "../../../../constants";
import { GUI } from "lil-gui";
import PlaneMarker from "./PlaneMarker.js";

/**
 * 流光粒子效果的Shader
 *
 * 功能特性：
 * - 支持渐变色效果（从头部颜色到尾部颜色）
 * - 流光动画效果，粒子沿路径移动
 * - 可配置的粒子大小、速度、长度等参数
 * - 基于百分比的颜色插值
 *
 * Uniforms:
 * - u_time: 动画时间
 * - number: 粒子数量
 * - speed: 动画速度
 * - length0: 流光长度
 * - size: 粒子大小
 * - startColor: 头部颜色
 * - endColor: 尾部颜色
 *
 * Attributes:
 * - percent: 粒子在路径上的百分比位置 (0.0 - 1.0)
 */
const flowingParticleShader = {
  vertexShader: `
    #include <common>
    #include <logdepthbuf_pars_vertex>
    varying vec2 vUv;
    attribute float percent;
    uniform float u_time;
    uniform float number;
    uniform float speed;
    uniform float length0;
    uniform float size;
    varying float opacity;
    varying float vPercent;

    void main() {
      vUv = uv;
      vPercent = percent;
      vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );
      float l = clamp(1.0-length0,0.0,1.0);
      float dis = length(mvPosition.xyz - cameraPosition);
      gl_PointSize = clamp(fract(percent*number + l - u_time*number*speed)-l ,0.0,1.) * size * (1./length0)*(1./-mvPosition.z);
      opacity = gl_PointSize;
      gl_Position = projectionMatrix * mvPosition;
      #include <logdepthbuf_vertex>
    }
  `,

  fragmentShader: `
    #ifdef GL_ES
    #include <common>
    #include <logdepthbuf_pars_fragment>
    precision mediump float;
    #endif
    varying float opacity;
    varying float vPercent;
    uniform vec3 startColor;
    uniform vec3 endColor;

    void main(){
      #include <logdepthbuf_fragment>
      if(opacity <=0.1 || distance(gl_PointCoord, vec2(0.5)) > 0.5){
        discard;
      }

      // 根据百分比计算渐变色
      vec3 gradientColor = mix(startColor, endColor, vPercent);
      gl_FragColor = vec4(gradientColor, 1.0);
    }
  `,
};

/**
 * 优化后的飞线管理类
 * 职责明确分离：
 * 1. 飞线创建和管理
 * 2. 粒子动画系统
 * 3. GUI控制界面
 */
class FlightLineOptimized {
  constructor(spherePoints) {
    this.spherePoints = spherePoints;

    // 核心数据
    this.flightLines = [];
    this.animationId = null;
    this.isAnimating = false;

    // GUI控制
    this.gui = null;

    // 配置参数
    this.config = {
      pointSize: 130.0,
      animationSpeed: 10.0,
      startColor: "#00fffb",
      endColor: "#002aff",
      flowingLength: 0.4,
      flowingSpeed: 310.0,
      blendingMode: "additive",
      segmentDivider: 400,
    };

    this._initialize();
  }

  /**
   * 初始化系统
   */
  _initialize() {
    // this._createGUI();
    this._startAnimation();
  }

  /**
   * 创建GUI控制面板
   */
  _createGUI() {
    if (this.gui) {
      this.gui.destroy();
    }

    this.gui = new GUI({ title: "飞线粒子控制" });

    this.gui
      .add(this.config, "pointSize", 10.0, 300.0, 10.0)
      .name("粒子大小")
      .onChange(() => this._updateAllFlightLines());

    this.gui.add(this.config, "animationSpeed", 0.1, 5.0, 0.1).name("动画速度");

    this.gui
      .addColor(this.config, "startColor")
      .name("头部颜色")
      .onChange(() => this._updateAllFlightLines());

    this.gui
      .addColor(this.config, "endColor")
      .name("尾部颜色")
      .onChange(() => this._updateAllFlightLines());
  }

  /**
   * 开始动画循环
   */
  _startAnimation() {
    if (this.isAnimating) return;

    this.isAnimating = true;

    const animate = () => {
      if (!this.isAnimating) return;

      this.animationId = requestAnimationFrame(animate);
      const timeIncrement = 0.0005 * this.config.animationSpeed;

      // 更新所有飞线粒子
      this.flightLines.forEach((flight) => {
        if (flight.particles) {
          flight.particles.forEach((particle) => {
            if (particle.material?.uniforms?.u_time) {
              particle.material.uniforms.u_time.value += timeIncrement;
            }
          });
        }
      });
    };

    animate();
  }

  /**
   * 停止动画
   */
  _stopAnimation() {
    this.isAnimating = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 创建飞线
   *
   * 这是创建飞线的主要方法，包含以下步骤：
   * 1. 根据起终点坐标创建弧形飞线
   * 2. 在飞线路径上创建流光粒子效果
   * 3. 将飞线数据保存到管理列表中
   *
   * @param {number} lat1 - 起点纬度 (-90 到 90)
   * @param {number} lon1 - 起点经度 (-180 到 180)
   * @param {number} lat2 - 终点纬度 (-90 到 90)
   * @param {number} lon2 - 终点经度 (-180 到 180)
   * @param {Object} [options={}] - 飞线配置选项
   * @param {Object} [options.lineOptions] - 线条样式配置
   * @param {number} [options.lineOptions.color=0x00ffff] - 线条颜色
   * @param {number} [options.lineOptions.opacity=0.6] - 线条透明度
   * @param {number} [options.lineOptions.segments=50] - 线条分段数
   * @param {Object} [options.animationOptions] - 动画配置
   * @returns {Object} 飞线数据对象，包含id、line、particles等信息
   *
   * @example
   * // 创建北京到东京的飞线
   * const flightLine = flightLineManager.createFlightLine(
   *   39.9042, 116.4074, // 北京
   *   35.6762, 139.6503, // 东京
   *   {
   *     lineOptions: {
   *       color: 0x00ffff,
   *       opacity: 0.8,
   *       segments: 60
   *     }
   *   }
   * );
   */
  createFlightLine(lat1, lon1, lat2, lon2, options = {}) {
    const flightLine = this._createCurvedLine(lat1, lon1, lat2, lon2, options.lineOptions || {});
    const particles = this._createFlightParticles(flightLine);

    const flightLineData = {
      id: `flight_${Date.now()}`,
      line: flightLine,
      particles,
      startCoords: { lat: lat1, lon: lon1 },
      endCoords: { lat: lat2, lon: lon2 },
      options,
    };

    this.flightLines.push(flightLineData);
    console.log(`飞线已创建: (${lat1}°, ${lon1}°) -> (${lat2}°, ${lon2}°)`);

    return flightLineData;
  }

  /**
   * 创建弧形飞线
   */
  _createCurvedLine(lat1, lon1, lat2, lon2, options) {
    const defaultOptions = {
      color: 0x00ffff,
      opacity: 0.6,
      segments: 50,
    };

    const config = { ...defaultOptions, ...options };

    // 获取起点和终点的3D坐标 - 稍微高于地球表面避免被地球网格覆盖
    const heightOffset = 0.1; // 高度偏移，让飞线显示在地球表面之上
    const startPos = this.spherePoints.latLonToVector3(lat1, lon1, EARTH_RADIUS + heightOffset);
    const endPos = this.spherePoints.latLonToVector3(lat2, lon2, EARTH_RADIUS + heightOffset);

    // 计算弧形高度
    const dis = startPos.distanceTo(endPos);
    // 设置最小高度限制，确保即使距离很近的点也有足够的弧度
    const minHeight = 2.0; // 最小弧高
    const calculatedHeight = dis * 0.4;
    const arcHeight = Math.max(minHeight, calculatedHeight);

    // 创建弧形曲线
    const midPoint = new THREE.Vector3()
      .addVectors(startPos, endPos)
      .multiplyScalar(0.5)
      .normalize()
      .multiplyScalar(EARTH_RADIUS + arcHeight);

    const curve = new THREE.QuadraticBezierCurve3(startPos, midPoint, endPos);
    console.log("🚀 ~ FlightLineOptimized ~ _createCurvedLine ~ curve:", curve);
    const points = curve.getPoints(config.segments);

    // 创建线条几何体和材质
    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    const material = new THREE.LineBasicMaterial({
      color: config.color,
      transparent: true,
      opacity: config.opacity,
    });

    const line = new THREE.Line(geometry, material);
    line.name = `FlightLine_${Date.now()}`;
    line.userData = { curve, curveLength: curve.getLength() };

    // 在起点位置创建一个平面标记（红色）
    const startPlane = PlaneMarker.createStartMarker(startPos, {
      size: 0.8,
      opacity: 0.8,
      ringColor: 0xf6fefe,
    });

    // 在终点位置创建一个平面标记（绿色）
    const endPlane = PlaneMarker.createEndMarker(endPos, {
      size: 0.8,
      opacity: 0.8,
    });

    // 添加到场景
    this.spherePoints.pointsGroup.add(line);
    this.spherePoints.pointsGroup.add(startPlane);
    this.spherePoints.pointsGroup.add(endPlane);

    return line;
  }

  /**
   * 创建飞行粒子
   */
  _createFlightParticles(flightLine) {
    const curve = flightLine.userData?.curve;
    if (!curve) {
      console.warn("飞线缺少曲线信息");
      return [];
    }

    // 计算分段数
    const segment = Math.ceil(curve.getLength()) * 50;
    const stepPts = curve.getSpacedPoints(segment);

    // 创建几何体
    const lineGeo = new THREE.BufferGeometry().setFromPoints(stepPts);
    const length = stepPts.length;
    const percents = new Float32Array(length);
    for (let i = 0; i < length; i++) {
      percents[i] = i / length;
    }
    lineGeo.setAttribute("percent", new THREE.BufferAttribute(percents, 1));

    // 创建材质
    const flowMat = this._createParticleMaterial(segment);

    // 创建粒子系统
    const point = new THREE.Points(lineGeo, flowMat);
    point.scale.copy(flightLine.scale);

    // 添加到场景
    this.spherePoints.pointsGroup.add(point);

    return [{ point, material: flowMat, geometry: lineGeo }];
  }

  /**
   * 创建粒子材质
   */
  _createParticleMaterial(segment) {
    return new THREE.ShaderMaterial({
      uniforms: {
        u_time: { value: 2.0 },
        number: { value: Math.round((segment / this.config.segmentDivider) * 0.7) || 1 },
        speed: { value: this.config.flowingSpeed / Math.round(segment / 50) || 1 },
        length0: { value: this.config.flowingLength * 1.5 },
        size: { value: this.config.pointSize },
        startColor: { value: new THREE.Color(this.config.startColor) },
        endColor: { value: new THREE.Color(this.config.endColor) },
      },
      vertexShader: flowingParticleShader.vertexShader,
      fragmentShader: flowingParticleShader.fragmentShader,
      transparent: true,
      depthWrite: false,
      blending: this._getBlendingMode(this.config.blendingMode),
    });
  }

  /**
   * 获取混合模式
   */
  _getBlendingMode(mode) {
    const modes = {
      normal: THREE.NormalBlending,
      additive: THREE.AdditiveBlending,
      subtractive: THREE.SubtractiveBlending,
      multiply: THREE.MultiplyBlending,
    };
    return modes[mode] || THREE.AdditiveBlending;
  }

  /**
   * 更新所有飞线
   */
  _updateAllFlightLines() {
    this.flightLines.forEach((flight) => {
      if (flight.particles) {
        flight.particles.forEach((particle) => {
          if (particle.material?.uniforms) {
            particle.material.uniforms.size.value = this.config.pointSize;
            particle.material.uniforms.startColor.value = new THREE.Color(this.config.startColor);
            particle.material.uniforms.endColor.value = new THREE.Color(this.config.endColor);
            particle.material.needsUpdate = true;
          }
        });
      }
    });
  }

  /**
   * 清除所有飞线
   */
  clearAllFlightLines() {
    this.flightLines.forEach((flight) => {
      // 移除线条
      if (flight.line) {
        this.spherePoints.pointsGroup.remove(flight.line);
        flight.line.geometry?.dispose();
        flight.line.material?.dispose();
      }

      // 移除粒子
      if (flight.particles) {
        flight.particles.forEach((particle) => {
          this.spherePoints.pointsGroup.remove(particle.point);
          particle.geometry?.dispose();
          particle.material?.dispose();
        });
      }
    });

    this.flightLines = [];
    console.log("所有飞线已清除");
  }

  /**
   * 销毁系统
   */
  destroy() {
    this._stopAnimation();
    this.clearAllFlightLines();

    if (this.gui) {
      this.gui.destroy();
      this.gui = null;
    }

    this.spherePoints = null;
    console.log("FlightLine系统已销毁");
  }

  // === 公共API ===

  /**
   * 获取配置
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 设置配置
   */
  setConfig(newConfig) {
    Object.assign(this.config, newConfig);
    this._updateAllFlightLines();
  }

  /**
   * 获取所有飞线信息
   */
  getFlightLines() {
    return this.flightLines.map((flight) => ({
      id: flight.id,
      startCoords: flight.startCoords,
      endCoords: flight.endCoords,
      options: flight.options,
    }));
  }

  /**
   * 更新所有点的位置（兼容性方法）
   */
  updateAllPoints() {
    // 这个方法保留用于兼容性，实际上飞线不需要更新点位置
    console.log("飞线点位置更新（无操作）");
  }
}

export { FlightLineOptimized };
export default FlightLineOptimized;
