<template>
  <div class="Qx1">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

const chartRef = ref(null);
let myChart = null;

// 颜色配置
const colorConfig = {
  高危: "#FD9391", // 红色
  中危: "#97C3FD", // 蓝色
  底危: "#32FEFC", // 绿色
};

// 计算属性：从 dataStore 获取动态数据
const chartData = computed(() => {
  const warningSeven = dataStore.data?.warningSeven;
  const warningSevenItem = dataStore.data?.warningSevenItem;

  if (!warningSeven || !warningSevenItem) {
    // 如果数据还未加载，返回默认值
    return {
      xAxisData: ["07-13", "07-14", "07-15", "07-16", "07-17", "07-18", "07-19"],
      highData: [0, 0, 0, 0, 0, 0, 0],
      middleData: [0, 0, 0, 0, 0, 0, 0],
      lowData: [0, 0, 0, 0, 0, 0, 0],
    };
  }

  return {
    xAxisData: warningSevenItem || [],
    highData: warningSeven.high || [],
    middleData: warningSeven.middle || [],
    lowData: warningSeven.low || [],
  };
});

const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);
    updateChart();

    // 响应式处理
    window.addEventListener("resize", () => {
      myChart && myChart.resize();
    });
  }
};

const updateChart = () => {
  if (!myChart) return;

  const option = {
    backgroundColor: "transparent",
    legend: {
      data: [
        { name: "高危", itemStyle: { color: colorConfig.高危 } },
        { name: "中危", itemStyle: { color: colorConfig.中危 } },
        { name: "底危", itemStyle: { color: colorConfig.底危 } },
      ],
      right: "10%",
      top: "5%",
      textStyle: {
        color: "#ffffff",
        fontSize: 12,
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 15,
      //圆圈
      icon: "circle",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.value.xAxisData,
      axisLine: {
        lineStyle: {
          color: "#00d4ff",
        },
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: "高危",
        data: chartData.value.highData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.高危,
          width: 2,
        },
      },
      {
        name: "中危",
        data: chartData.value.middleData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.中危,
          width: 2,
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: colorConfig.中危 + "66", // 顶部颜色，40%透明度
              },
              {
                offset: 1,
                color: colorConfig.中危 + "0D", // 底部颜色，5%透明度
              },
            ],
          },
        },
      },
      {
        name: "底危",
        data: chartData.value.lowData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.底危,
          width: 2,
        },
      },
    ],
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#00d4ff",
      textStyle: {
        color: "#ffffff",
      },
    },
  };

  myChart.setOption(option);
};

// 监听数据变化，自动更新图表
watch(
  chartData,
  () => {
    updateChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", () => {
    myChart && myChart.resize();
  });
});
</script>

<style lang="less" scoped>
.Qx1 {
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
