import * as THREE from "three";
import { G<PERSON> } from "lil-gui";
import { EARTH_FRAGMENTS, EARTH_RADIUS, PATHS } from "../../../../constants/index.js";
import { texturePreloader } from "../../utils/TexturePreloader.js";

class EarthDay {
  constructor(scene, { onLoad, renderer, enableGUI = true } = {}) {
    this.scene = scene;
    this.onLoad = onLoad;
    this.renderer = renderer;
    this.enableGUI = enableGUI;
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {
      day: null,
      bump: null,
      specular: null,
    };

    // GUI 相关属性
    this.gui = null;
    this.folder = null;

    // Material parameters for GUI control
    this.materialParams = {
      // 基础属性
      color: "#ffffff",
      opacity: 1.0,
      transparent: true,

      // 物理属性
      metalness: 0,
      roughness: 1.0,

      // 凹凸贴图
      bumpScale: 12.4,

      // 各向异性
      anisotropy: 0.0,
      anisotropyRotation: 0.0,

      // 清漆层
      clearcoat: 0.0,
      clearcoatRoughness: 0.0,

      // 折射率
      ior: 2.33,
      reflectivity: 1,

      // 彩虹色
      iridescence: 0.0,
      iridescenceIOR: 1.3,

      // 光泽
      sheen: 0.0,
      sheenRoughness: 1.0,
      sheenColor: "#000000",

      // 镜面反射
      specularIntensity: 1.0,
      specularColor: "#ffffff",

      // 透射
      transmission: 0.0,
      thickness: 0.0,
      attenuationDistance: Infinity,
      attenuationColor: "#ffffff",
      dispersion: 0.0,
    };

    this.init();
  }

  async init() {
    await this.loadTextures();
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();

    // Initialize GUI controls
    if (this.enableGUI) {
      // this.initGUI();
    }

    // Call onLoad callback if provided
    if (this.onLoad) {
      this.onLoad();
    }
  }

  async loadTextures() {
    try {
      // 优先使用预加载的纹理
      const preloadedTextures = texturePreloader.getAllTextures();

      if (preloadedTextures.earthMap && preloadedTextures.bumpMap && preloadedTextures.specularMap) {
        console.log("EarthDay: 使用预加载的纹理");

        this.textures.day = preloadedTextures.earthMap;
        this.textures.bump = preloadedTextures.bumpMap;
        this.textures.specular = preloadedTextures.specularMap;
      } else {
        console.log("EarthDay: 预加载纹理不可用，使用异步加载");

        // 如果预加载纹理不可用，使用传统的异步加载方式
        const textureLoader = new THREE.TextureLoader();
        const [dayTexture, bumpTexture, specularTexture] = await Promise.all([
          this.loadTexture(textureLoader, PATHS.earthMap),
          this.loadTexture(textureLoader, PATHS.bumpMap),
          this.loadTexture(textureLoader, PATHS.specularMap),
        ]);

        this.textures.day = dayTexture;
        this.textures.bump = bumpTexture;
        this.textures.specular = specularTexture;
      }

      // Set color space for proper color rendering
      this.textures.day.colorSpace = THREE.SRGBColorSpace; // Color texture needs sRGB
      this.textures.bump.colorSpace = THREE.NoColorSpace; // Data texture, no color space
      this.textures.specular.colorSpace = THREE.NoColorSpace; // Data texture, no color space

      // Set anisotropy for better quality - get actual max from renderer
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      this.textures.day.anisotropy = maxAnisotropy;
      this.textures.bump.anisotropy = maxAnisotropy;
      this.textures.specular.anisotropy = maxAnisotropy;

      console.log("EarthDay: 纹理加载完成");
    } catch (error) {
      console.error("Error loading EarthDay textures:", error);
    }
  }

  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  createGeometry() {
    this.geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS);
  }

  createMaterial() {
    this.material = new THREE.MeshPhysicalMaterial({
      // 贴图
      map: this.textures.day,
      bumpMap: this.textures.bump,
      specularMap: this.textures.specular,

      // 基础属性
      color: new THREE.Color(this.materialParams.color),
      opacity: this.materialParams.opacity,
      transparent: this.materialParams.transparent,

      // 物理属性
      metalness: this.materialParams.metalness,
      roughness: this.materialParams.roughness,

      // 凹凸贴图
      bumpScale: this.materialParams.bumpScale,

      // 各向异性
      anisotropy: this.materialParams.anisotropy,
      anisotropyRotation: this.materialParams.anisotropyRotation,

      // 清漆层
      clearcoat: this.materialParams.clearcoat,
      clearcoatRoughness: this.materialParams.clearcoatRoughness,

      // 折射率
      ior: this.materialParams.ior,
      reflectivity: this.materialParams.reflectivity,

      // 彩虹色
      iridescence: this.materialParams.iridescence,
      iridescenceIOR: this.materialParams.iridescenceIOR,

      // 光泽
      sheen: this.materialParams.sheen,
      sheenRoughness: this.materialParams.sheenRoughness,
      sheenColor: new THREE.Color(this.materialParams.sheenColor),

      // 镜面反射
      specularIntensity: this.materialParams.specularIntensity,
      specularColor: new THREE.Color(this.materialParams.specularColor),

      // 透射
      transmission: this.materialParams.transmission,
      thickness: this.materialParams.thickness,
      attenuationDistance: this.materialParams.attenuationDistance,
      attenuationColor: new THREE.Color(this.materialParams.attenuationColor),
      dispersion: this.materialParams.dispersion,
    });
  }

  createMesh() {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.castShadow = true;
    this.mesh.scale.set(0.999, 0.999, 0.999);
    this.mesh.rotation.y = Math.PI;
    this.mesh.renderOrder = 0; // 地球作为基础层
  }

  addToScene() {
    if (this.scene && this.mesh) {
      // this.mesh.visible = false;
      this.scene.add(this.mesh);
    }
  }

  /**
   * 初始化GUI控制面板
   */
  initGUI() {
    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建地球材质控制文件夹
      this.folder = this.gui.addFolder("🌍 地球材质 (Earth Material)");
      this.folder.open();

      this.setupGUIControls();
    } catch (error) {
      console.warn("EarthDay GUI setup warning:", error);
    }
  }

  /**
   * 设置GUI控制项
   */
  setupGUIControls() {
    if (!this.folder || !this.material) return;

    // 基础属性文件夹
    const basicFolder = this.folder.addFolder("🎨 基础属性 (Basic Properties)");
    basicFolder.open();

    // 颜色控制
    basicFolder
      .addColor(this.materialParams, "color")
      .name("颜色 (Color)")
      .onChange((value) => {
        this.material.color.set(value);
      });

    // 透明度控制
    basicFolder
      .add(this.materialParams, "opacity", 0, 1, 0.01)
      .name("透明度 (Opacity)")
      .onChange((value) => {
        this.material.opacity = value;
      });

    // 透明开关
    basicFolder
      .add(this.materialParams, "transparent")
      .name("透明 (Transparent)")
      .onChange((value) => {
        this.material.transparent = value;
      });

    // 物理属性文件夹
    const physicalFolder = this.folder.addFolder("⚡ 物理属性 (Physical Properties)");
    physicalFolder.open();

    // 金属度
    physicalFolder
      .add(this.materialParams, "metalness", 0, 1, 0.01)
      .name("金属度 (Metalness)")
      .onChange((value) => {
        this.material.metalness = value;
      });

    // 粗糙度
    physicalFolder
      .add(this.materialParams, "roughness", 0, 1, 0.01)
      .name("粗糙度 (Roughness)")
      .onChange((value) => {
        this.material.roughness = value;
      });

    // 凹凸贴图强度
    physicalFolder
      .add(this.materialParams, "bumpScale", 0, 50, 0.1)
      .name("凹凸强度 (Bump Scale)")
      .onChange((value) => {
        this.material.bumpScale = value;
      });

    // 折射率
    physicalFolder
      .add(this.materialParams, "ior", 1, 2.333, 0.01)
      .name("折射率 (IOR)")
      .onChange((value) => {
        this.material.ior = value;
      });

    // 反射率
    physicalFolder
      .add(this.materialParams, "reflectivity", 0, 1, 0.01)
      .name("反射率 (Reflectivity)")
      .onChange((value) => {
        this.material.reflectivity = value;
      });

    // 各向异性文件夹
    const anisotropyFolder = this.folder.addFolder("🔄 各向异性 (Anisotropy)");

    // 各向异性强度
    anisotropyFolder
      .add(this.materialParams, "anisotropy", 0, 1, 0.01)
      .name("强度 (Strength)")
      .onChange((value) => {
        this.material.anisotropy = value;
      });

    // 各向异性旋转
    anisotropyFolder
      .add(this.materialParams, "anisotropyRotation", 0, Math.PI * 2, 0.01)
      .name("旋转 (Rotation)")
      .onChange((value) => {
        this.material.anisotropyRotation = value;
      });

    // 清漆层文件夹
    const clearcoatFolder = this.folder.addFolder("✨ 清漆层 (Clearcoat)");

    // 清漆强度
    clearcoatFolder
      .add(this.materialParams, "clearcoat", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.clearcoat = value;
      });

    // 清漆粗糙度
    clearcoatFolder
      .add(this.materialParams, "clearcoatRoughness", 0, 1, 0.01)
      .name("粗糙度 (Roughness)")
      .onChange((value) => {
        this.material.clearcoatRoughness = value;
      });

    // 彩虹色文件夹
    const iridescenceFolder = this.folder.addFolder("🌈 彩虹色 (Iridescence)");

    // 彩虹色强度
    iridescenceFolder
      .add(this.materialParams, "iridescence", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.iridescence = value;
      });

    // 彩虹色折射率
    iridescenceFolder
      .add(this.materialParams, "iridescenceIOR", 1, 2.333, 0.01)
      .name("折射率 (IOR)")
      .onChange((value) => {
        this.material.iridescenceIOR = value;
      });

    // 光泽文件夹
    const sheenFolder = this.folder.addFolder("🧶 光泽 (Sheen)");

    // 光泽强度
    sheenFolder
      .add(this.materialParams, "sheen", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.sheen = value;
      });

    // 光泽粗糙度
    sheenFolder
      .add(this.materialParams, "sheenRoughness", 0, 1, 0.01)
      .name("粗糙度 (Roughness)")
      .onChange((value) => {
        this.material.sheenRoughness = value;
      });

    // 光泽颜色
    sheenFolder
      .addColor(this.materialParams, "sheenColor")
      .name("颜色 (Color)")
      .onChange((value) => {
        this.material.sheenColor.set(value);
      });

    // 镜面反射文件夹
    const specularFolder = this.folder.addFolder("💎 镜面反射 (Specular)");

    // 镜面反射强度
    specularFolder
      .add(this.materialParams, "specularIntensity", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.specularIntensity = value;
      });

    // 镜面反射颜色
    specularFolder
      .addColor(this.materialParams, "specularColor")
      .name("颜色 (Color)")
      .onChange((value) => {
        this.material.specularColor.set(value);
      });

    // 透射文件夹
    const transmissionFolder = this.folder.addFolder("🔍 透射 (Transmission)");

    // 透射强度
    transmissionFolder
      .add(this.materialParams, "transmission", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.transmission = value;
      });

    // 厚度
    transmissionFolder
      .add(this.materialParams, "thickness", 0, 10, 0.01)
      .name("厚度 (Thickness)")
      .onChange((value) => {
        this.material.thickness = value;
      });

    // 衰减距离
    transmissionFolder
      .add(this.materialParams, "attenuationDistance", 0.1, 100, 0.1)
      .name("衰减距离 (Attenuation Distance)")
      .onChange((value) => {
        this.material.attenuationDistance = value;
      });

    // 衰减颜色
    transmissionFolder
      .addColor(this.materialParams, "attenuationColor")
      .name("衰减颜色 (Attenuation Color)")
      .onChange((value) => {
        this.material.attenuationColor.set(value);
      });

    // 色散
    transmissionFolder
      .add(this.materialParams, "dispersion", 0, 1, 0.01)
      .name("色散 (Dispersion)")
      .onChange((value) => {
        this.material.dispersion = value;
      });

    // 预设按钮文件夹
    const presetsFolder = this.folder.addFolder("🎯 预设 (Presets)");
    presetsFolder.open();

    const presets = {
      resetToDefault: () => this.resetToDefault(),
      earthLike: () => this.applyEarthPreset(),
      metallic: () => this.applyMetallicPreset(),
      glass: () => this.applyGlassPreset(),
      fabric: () => this.applyFabricPreset(),
    };

    presetsFolder.add(presets, "resetToDefault").name("🔄 重置默认");
    presetsFolder.add(presets, "earthLike").name("🌍 地球风格");
    presetsFolder.add(presets, "metallic").name("⚡ 金属风格");
    presetsFolder.add(presets, "glass").name("🔍 玻璃风格");
    presetsFolder.add(presets, "fabric").name("🧶 织物风格");
  }

  /**
   * 重置为默认值
   */
  resetToDefault() {
    this.materialParams = {
      color: "#ffffff",
      opacity: 1.0,
      transparent: true,
      metalness: 0.8,
      roughness: 1.0,
      bumpScale: 15.75,
      anisotropy: 0.0,
      anisotropyRotation: 0.0,
      clearcoat: 0.0,
      clearcoatRoughness: 0.0,
      ior: 1.5,
      reflectivity: 0.5,
      iridescence: 0.0,
      iridescenceIOR: 1.3,
      sheen: 0.0,
      sheenRoughness: 1.0,
      sheenColor: "#000000",
      specularIntensity: 1.0,
      specularColor: "#ffffff",
      transmission: 0.0,
      thickness: 0.0,
      attenuationDistance: Infinity,
      attenuationColor: "#ffffff",
      dispersion: 0.0,
    };
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用地球风格预设
   */
  applyEarthPreset() {
    Object.assign(this.materialParams, {
      metalness: 0.1,
      roughness: 0.8,
      bumpScale: 15.75,
      clearcoat: 0.0,
      transmission: 0.0,
      sheen: 0.0,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用金属风格预设
   */
  applyMetallicPreset() {
    Object.assign(this.materialParams, {
      metalness: 1.0,
      roughness: 0.2,
      anisotropy: 0.8,
      clearcoat: 0.5,
      clearcoatRoughness: 0.1,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用玻璃风格预设
   */
  applyGlassPreset() {
    Object.assign(this.materialParams, {
      metalness: 0.0,
      roughness: 0.0,
      transmission: 0.9,
      thickness: 1.0,
      ior: 1.5,
      opacity: 1.0,
      transparent: true,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用织物风格预设
   */
  applyFabricPreset() {
    Object.assign(this.materialParams, {
      metalness: 0.0,
      roughness: 1.0,
      sheen: 1.0,
      sheenRoughness: 0.8,
      sheenColor: "#ffffff",
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 从参数更新材质
   */
  updateMaterialFromParams() {
    if (!this.material) return;

    this.material.color.set(this.materialParams.color);
    this.material.opacity = this.materialParams.opacity;
    this.material.transparent = this.materialParams.transparent;
    this.material.metalness = this.materialParams.metalness;
    this.material.roughness = this.materialParams.roughness;
    this.material.bumpScale = this.materialParams.bumpScale;
    this.material.anisotropy = this.materialParams.anisotropy;
    this.material.anisotropyRotation = this.materialParams.anisotropyRotation;
    this.material.clearcoat = this.materialParams.clearcoat;
    this.material.clearcoatRoughness = this.materialParams.clearcoatRoughness;
    this.material.ior = this.materialParams.ior;
    this.material.reflectivity = this.materialParams.reflectivity;
    this.material.iridescence = this.materialParams.iridescence;
    this.material.iridescenceIOR = this.materialParams.iridescenceIOR;
    this.material.sheen = this.materialParams.sheen;
    this.material.sheenRoughness = this.materialParams.sheenRoughness;
    this.material.sheenColor.set(this.materialParams.sheenColor);
    this.material.specularIntensity = this.materialParams.specularIntensity;
    this.material.specularColor.set(this.materialParams.specularColor);
    this.material.transmission = this.materialParams.transmission;
    this.material.thickness = this.materialParams.thickness;
    this.material.attenuationDistance = this.materialParams.attenuationDistance;
    this.material.attenuationColor.set(this.materialParams.attenuationColor);
    this.material.dispersion = this.materialParams.dispersion;

    this.material.needsUpdate = true;
  }

  /**
   * 从参数更新GUI显示
   */
  updateGUIFromParams() {
    if (!this.folder) return;

    // 遍历所有控制器并更新显示值
    this.folder.controllersRecursive().forEach((controller) => {
      controller.updateDisplay();
    });
  }

  destroy() {
    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Dispose of textures
    Object.values(this.textures).forEach((texture) => {
      if (texture) {
        texture.dispose();
      }
    });

    // Clean up GUI
    if (this.folder) {
      this.folder.destroy();
      this.folder = null;
    }

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {};
    this.gui = null;
  }
}

// Export both named and default exports for flexibility
export { EarthDay };
export default EarthDay;
