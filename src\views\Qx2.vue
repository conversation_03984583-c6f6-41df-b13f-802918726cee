<template>
  <div class="Qx2">
    <div ref="chartRef1" class="chart-container"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { usedata } from "../store/data";

const dataStore = usedata();
const chartRef1 = ref(null);
let myChart = null;

// 颜色配置
const colorConfig = {
  数据趋势: "#ffffff", // 白色
};

// 计算属性：从 dataStore 获取 colasoft 数据
const chartData = computed(() => {
  const colasoftData = dataStore.colasoftData;

  if (!colasoftData || !colasoftData.xAxisData || !colasoftData.seriesData) {
    // 如果数据还未加载，返回默认值
    return {
      xAxisData: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
      seriesData: [280, 380, 420, 480, 620, 700, 780, 850, 720, 600, 540, 480],
    };
  }

  return {
    xAxisData: colasoftData.xAxisData || [],
    seriesData: colasoftData.seriesData || [],
  };
});

const initChart = () => {
  if (chartRef1.value) {
    myChart = echarts.init(chartRef1.value);
    updateChart();

    // 响应式处理
    window.addEventListener("resize", () => {
      myChart && myChart.resize();
    });
  }
};

const updateChart = () => {
  if (!myChart) return;

  const option = {
    backgroundColor: "transparent",
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "10%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.value.xAxisData,
      axisLine: {
        lineStyle: {
          color: "#00d4ff",
        },
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: "数据趋势",
        data: chartData.value.seriesData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.数据趋势,
          width: 3,
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: colorConfig.数据趋势 + "80", // 顶部颜色，50%透明度
              },
              {
                offset: 1,
                color: colorConfig.数据趋势 + "10", // 底部颜色，6%透明度
              },
            ],
          },
        },
      },
    ],
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: colorConfig.数据趋势,
      textStyle: {
        color: "#ffffff",
      },
    },
  };

  myChart.setOption(option);
};

// 监听数据变化，自动更新图表
watch(
  chartData,
  () => {
    updateChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", () => {
    myChart && myChart.resize();
  });
});
</script>

<style lang="less" scoped>
.Qx2 {
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
