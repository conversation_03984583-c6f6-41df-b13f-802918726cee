import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
import { GUI } from "lil-gui";
import { EARTH_RADIUS } from "../../../../constants";
import { EarthDay } from "../EarthDay/EarthDay.js";
import { Clouds } from "../Clouds/Clouds.js";
import { EarthNight } from "../EarthNight/EarthNight.js";
import { Halo } from "../Halo/Halo.js";
import { Lights } from "../Lights/Lights.js";
import { HoneycombGrid } from "../HoneycombGrid/HoneycombGrid.js";
import { SpherePoints } from "../SpherePoints/SpherePoints.js";
import { StarField } from "../StarField/StarField.js";
import { OrbitingStars } from "../OrbitingStars/OrbitingStars.js";
import { PlanetRings } from "../PlanetRings/PlanetRings.js";
import { PostProcessingManager } from "./PostProcessingManager.js";
import { CameraAnimationController } from "../../utils/CameraAnimationController.js";
import { texturePreloader } from "../../utils/TexturePreloader.js";

class Scene {
  // Static instance for singleton pattern
  static instance = null;

  constructor(container, { sunCoordsRef, onLoad }) {
    // Prevent multiple instances
    if (Scene.instance) {
      console.warn("Scene is a singleton. Use Scene.getInstance() instead.");
      return Scene.instance;
    }

    this.container = container;
    this.sunCoordsRef = sunCoordsRef;
    this.onLoad = onLoad;

    // Three.js core objects
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.controls = null;

    // 纹理预加载相关
    this.texturePreloader = texturePreloader;
    this.preloadedTextures = null;
    this.isTexturesLoaded = false;

    this.earthGroup = new THREE.Group();
    this.earthGroup.name = "EarthGroup"; // 为调试添加名称

    // GUI相关属性
    this.gui = null;
    this.earthGroupFolder = null;
    this.earthGroupParams = {
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 },
      visible: true,
    };

    // 地球动画参数
    this.earthAnimationParams = {
      arcHeight: -15, // 弧形高度偏移量
      midPointPosition: 0.2, // 中间点位置比例（0=起点，0.5=中间，1=终点）
      positionDelay: 500, // 位移动画延迟时间（毫秒）
      duration: 3500, // 动画持续时间（毫秒）
      ringAnimationTriggerProgress: 0.7, // 圆环动画触发进度（0.5表示地球动画50%时开始圆环动画）
      flightLineAnimationTriggerProgress: 0.7, // 飞线动画触发进度（0.6表示地球动画60%时开始飞线动画）
      uiAnimationTriggerProgress: 0.85, // UI动画触发进度（0.85表示地球动画85%时开始UI动画）
    };

    // 圆环动画状态
    this.ringAnimationTriggered = false;

    // 飞线动画状态
    this.flightLineAnimationTriggered = false;

    // UI动画状态
    this.uiAnimationTriggered = false;

    // Component instances
    this.earthDay = null;
    this.clouds = null;
    this.earthNight = null;
    this.halo = null;
    this.lights = null;
    this.honeycombGrid = null;
    this.spherePoints = null;
    this.starField = null;
    this.orbitingStars = null;
    this.postProcessingManager = null;
    this.cameraAnimationController = null;

    // Animation
    this.animationId = null;
    this.isDestroyed = false;

    // Event system for component communication
    this.eventListeners = new Map();

    // Set singleton instance
    Scene.instance = this;

    // 暴露到全局变量，方便其他模块访问
    if (typeof window !== "undefined") {
      window.debugScene = this;
      window.Scene = Scene;
    }

    this.init();
  }

  // Static method to get singleton instance
  static getInstance() {
    return Scene.instance;
  }

  // Static method to create or get instance
  static createInstance(container, options) {
    if (!Scene.instance) {
      new Scene(container, options);
    }
    return Scene.instance;
  }

  // Event system methods
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  dispatchEvent(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  async init() {
    this.createScene();
    this.createCamera();
    this.createRenderer();
    this.createControls();
    this.createCameraAnimationController();

    // 预加载所有纹理
    await this.preloadTextures();

    this.createComponents();
    this.createPostProcessing();
    // this.initEarthGroupGUI();
    this.setupEventListeners();
    this.animate();

    // 等待纹理加载完成后启动地球动画
    this.startEarthAnimationWhenReady();

    // 启动相机拉远动画
    // this.startCameraZoomOutAnimation();
  }

  /**
   * 预加载所有纹理
   */
  async preloadTextures() {
    try {
      console.log("开始预加载地球纹理...");

      // 设置纹理预加载事件监听器
      this.texturePreloader.addEventListener("loadProgress", (data) => {
        console.log(`纹理加载进度: ${data.progress.toFixed(1)}%`);
        this.dispatchEvent("textureLoadProgress", data);
      });

      this.texturePreloader.addEventListener("loadComplete", (data) => {
        console.log("所有纹理预加载完成");
        this.isTexturesLoaded = true;
        this.dispatchEvent("textureLoadComplete", data);
      });

      this.texturePreloader.addEventListener("loadError", (data) => {
        console.error("纹理加载失败:", data.url);
        this.dispatchEvent("textureLoadError", data);
      });

      // 开始预加载
      this.preloadedTextures = await this.texturePreloader.preloadAllTextures();
      console.log("纹理预加载完成，已加载的纹理:", Object.keys(this.preloadedTextures));
    } catch (error) {
      console.error("纹理预加载失败:", error);
      // 即使预加载失败，也继续创建组件（使用默认的异步加载）
      this.isTexturesLoaded = false;
    }
  }

  /**
   * 等待纹理加载完成后启动地球动画
   */
  startEarthAnimationWhenReady() {
    if (this.isTexturesLoaded) {
      // 纹理已加载完成，立即启动动画
      console.log("纹理已预加载完成，立即启动地球动画");
      this.startEarthAnimation();
    } else {
      // 等待纹理加载完成
      console.log("等待纹理加载完成...");
      this.texturePreloader.addEventListener("loadComplete", () => {
        console.log("纹理加载完成，启动地球动画");
        this.startEarthAnimation();
      });
    }
  }

  createScene() {
    this.scene = new THREE.Scene();
  }

  createCamera() {
    const aspect = this.container.clientWidth / this.container.clientHeight;
    this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
    this.camera.position.set(6.607814630433295, 23.527156235789565, 33.78524722016197);

    window.addEventListener("keydown", (e) => {
      if (e.key == "q") {
        console.log(`${this.camera.position.x},${this.camera.position.y},${this.camera.position.z}`);
        console.log(`${this.controls.target.x},${this.controls.target.y},${this.controls.target.z}`);
      }
    });
  }

  createRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
    });

    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    //色调映射
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;

    this.container.appendChild(this.renderer.domElement);
  }

  createControls() {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, -1, -2.7);
    //禁止放大缩小
    this.controls.enableZoom = false;
    //禁止拖拽
    this.controls.enablePan = false;

    this.controls.minPolarAngle = Math.PI * 0.15;
    this.controls.maxPolarAngle = Math.PI * 0.5;
    // this.controls.minDistance = EARTH_RADIUS * 2;
    // this.controls.minDistance = 0;
    // this.controls.maxDistance = EARTH_RADIUS * 2.2;
    // this.controls.maxDistance = 100;
    // this.controls.enablePan = false;
    // this.controls.zoomSpeed = 0.04;
    // this.controls.autoRotate = true;
    // this.controls.autoRotateSpeed = 0.03;
    // this.controls.target.set(0, 0, 0);
  }

  createCameraAnimationController() {
    this.cameraAnimationController = new CameraAnimationController(this.camera, this.controls);
  }

  /**
   * 启动相机拉远动画
   */
  startCameraZoomOutAnimation() {
    if (this.cameraAnimationController) {
      // 延迟1秒后开始动画，让场景完全加载
      // 使用预设位置的相机动画
      this.cameraAnimationController.zoomOutWithPresetPositions({
        duration: 3000, // 3秒动画
        onStart: () => {
          console.log("相机拉远动画开始 - 使用预设位置");
          this.dispatchEvent("cameraZoomOutStart", {});
        },
        onUpdate: (progress, easedProgress) => {
          // 可以在这里添加动画更新时的逻辑
          this.dispatchEvent("cameraZoomOutUpdate", { progress, easedProgress });
        },
        onComplete: () => {
          console.log("相机拉远动画完成 - 已到达预设位置");
          this.dispatchEvent("cameraZoomOutComplete", {});
        },
      });
    }
  }

  /**
   * 启动圆环缩放动画
   * 从缩放0恢复到正常大小，第二个圆环动画时间更长
   */
  startRingScaleAnimation() {
    if (this.planetRings) {
      console.log("启动圆环缩放动画");

      // 使用自定义动画时长：第一个圆环1200ms，第二个圆环1800ms
      this.planetRings.animateRingsWithCustomTiming(
        [
          { targetScale: 1.0, duration: 1000, delay: 0 }, // 第一个圆环
          { targetScale: 1.0, duration: 1000, delay: 130 }, // 第二个圆环，延迟300ms开始，时长更长
        ],
        () => {
          console.log("圆环缩放动画完成，圆环已显示");
          this.dispatchEvent("ringScaleAnimationComplete", {});

          // 注意：飞线动画现在在地球动画60%时就会触发，不再在圆环动画完成后触发
        }
      );
    }
  }

  /**
   * 启动飞线动画序列
   * 在圆环动画完成后启动所有飞线的绘制和流光动画
   */
  startFlightLineAnimations() {
    if (this.spherePoints && this.spherePoints.flightLine) {
      console.log("启动飞线动画序列");

      const flightLineManager = this.spherePoints.flightLine;
      const flightLines = flightLineManager.getFlightLines();

      // 为每条飞线启动动画序列，添加递增延迟
      flightLines.forEach((flightLineInfo, index) => {
        // 根据索引添加延迟，让飞线依次出现
        const delay = index * 300; // 每条飞线延迟300ms

        setTimeout(() => {
          // 查找对应的飞线数据对象
          const flightLineData = flightLineManager.flightLines.find(
            (flight) =>
              flight.startCoords.lat === flightLineInfo.startCoords.lat &&
              flight.startCoords.lon === flightLineInfo.startCoords.lon &&
              flight.endCoords.lat === flightLineInfo.endCoords.lat &&
              flight.endCoords.lon === flightLineInfo.endCoords.lon
          );

          if (flightLineData) {
            flightLineManager.startFlightLineAnimation(flightLineData, {
              drawDuration: 1000, // 线条绘制时间
              flowDelay: 100, // 流光动画延迟
            });
          }
        }, delay);
      });

      this.dispatchEvent("flightLineAnimationsStarted", { count: flightLines.length });
    }
  }

  /**
   * 启动地球动画
   * 从起始位置和旋转动画到目标位置和旋转（使用弧形路径）
   * @param {Object} options - 动画选项（可选）
   */
  startEarthAnimation(options = {}) {
    if (this.cameraAnimationController && this.earthGroup) {
      // 重置动画触发状态
      this.ringAnimationTriggered = false;
      this.flightLineAnimationTriggered = false;
      this.uiAnimationTriggered = false;

      const finalOptions = {
        duration: this.earthAnimationParams.duration,
        arcHeight: this.earthAnimationParams.arcHeight,
        midPointPosition: this.earthAnimationParams.midPointPosition,
        positionDelay: this.earthAnimationParams.positionDelay,
        ...options, // 允许覆盖默认参数
      };

      this.cameraAnimationController.animateEarth(this.earthGroup, {
        ...finalOptions,
        onStart: () => {
          console.log("地球动画开始 - 弧形高度:", finalOptions.arcHeight, "中间点位置:", finalOptions.midPointPosition, "位移延迟:", finalOptions.positionDelay + "ms");
          this.dispatchEvent("earthAnimationStart", {
            arcHeight: finalOptions.arcHeight,
            midPointPosition: finalOptions.midPointPosition,
            positionDelay: finalOptions.positionDelay,
          });
        },
        onUpdate: (progress, easedProgress, data) => {
          // 检查是否需要提前触发圆环动画
          if (!this.ringAnimationTriggered && progress >= this.earthAnimationParams.ringAnimationTriggerProgress) {
            this.ringAnimationTriggered = true;
            console.log(`地球动画进度达到${(this.earthAnimationParams.ringAnimationTriggerProgress * 100).toFixed(0)}%，提前启动圆环动画`);
            this.startRingScaleAnimation();
          }

          // 检查是否需要提前触发飞线动画
          if (!this.flightLineAnimationTriggered && progress >= this.earthAnimationParams.flightLineAnimationTriggerProgress) {
            this.flightLineAnimationTriggered = true;
            console.log(`地球动画进度达到${(this.earthAnimationParams.flightLineAnimationTriggerProgress * 100).toFixed(0)}%，提前启动飞线动画`);
            this.startFlightLineAnimations();
          }

          // 检查是否需要触发UI动画
          if (!this.uiAnimationTriggered && progress >= this.earthAnimationParams.uiAnimationTriggerProgress) {
            this.uiAnimationTriggered = true;
            console.log(`地球动画进度达到${(this.earthAnimationParams.uiAnimationTriggerProgress * 100).toFixed(0)}%，触发UI动画`);
            this.dispatchEvent("uiAnimationTrigger", {
              progress,
              easedProgress,
              triggerProgress: this.earthAnimationParams.uiAnimationTriggerProgress,
            });
          }

          // 可以在这里添加动画更新时的逻辑
          this.dispatchEvent("earthAnimationUpdate", {
            progress,
            easedProgress,
            position: data.position,
            rotation: data.rotation,
            midPoint: data.midPoint, // 包含中间点信息
            ringAnimationTriggered: this.ringAnimationTriggered, // 圆环动画是否已触发
            flightLineAnimationTriggered: this.flightLineAnimationTriggered, // 飞线动画是否已触发
          });
        },
        onComplete: () => {
          console.log("地球动画完成");
          this.dispatchEvent("earthAnimationComplete", {});

          // 如果圆环动画还没有触发（防止意外情况），在这里触发
          if (!this.ringAnimationTriggered) {
            console.log("地球动画完成时圆环动画尚未触发，现在启动");
            this.ringAnimationTriggered = true;
            this.startRingScaleAnimation();
          }

          // 如果飞线动画还没有触发（防止意外情况），在这里触发
          if (!this.flightLineAnimationTriggered) {
            console.log("地球动画完成时飞线动画尚未触发，现在启动");
            this.flightLineAnimationTriggered = true;
            this.startFlightLineAnimations();
          }

          // 重置动画触发状态，为下次动画做准备
          this.ringAnimationTriggered = false;
          this.flightLineAnimationTriggered = false;
          this.uiAnimationTriggered = false;
        },
      });
    }
  }

  createComponents() {
    // Create component instances with renderer reference for better texture quality
    // 球体相关组件使用 earthGroup 作为父容器
    this.earthDay = new EarthDay(this.earthGroup, { onLoad: this.onLoad, renderer: this.renderer });
    this.clouds = new Clouds(this.earthGroup, { renderer: this.renderer });
    this.earthNight = new EarthNight(this.earthGroup, { renderer: this.renderer });
    this.halo = new Halo(this.earthGroup, { color: "#7da4ff" });

    // 将 earthGroup 添加到主场景中
    this.scene.add(this.earthGroup);

    // 灯光组件也添加到 earthGroup 中，便于整体控制
    this.lights = new Lights(this.earthGroup, this.camera, this.controls, {
      shouldOrbit: true,
      sunCoordsRef: this.sunCoordsRef,
      enableGUI: true, // 设置为 false 可以禁用GUI和点光源辅助器
    });
    this.honeycombGrid = new HoneycombGrid(this.earthGroup, {
      renderer: this.renderer,
      opacity: 0.4,
      color: "#00ccff",
    });
    this.spherePoints = new SpherePoints(this.earthGroup, {
      renderer: this.renderer,
      onLoad: () => {
        console.log("SpherePoints loaded successfully");
      },
    });
    // Create star field for space atmosphere
    this.starField = new StarField(this.scene, {
      starCount: 8000,
      radius: 500,
      enableTwinkle: true,
      twinkleSpeed: 0.002,
      color: 0xffffff,
    });
    // Create orbiting stars around Earth (添加到 earthGroup 中)
    this.orbitingStars = new OrbitingStars(this.earthGroup, {});
    // Create planet rings around Earth
    this.planetRings = new PlanetRings(this.earthGroup, {
      segments: 128,
      enableGUI: true,
      visible: true,
    });
  }

  createPostProcessing() {
    // 创建后处理管理器
    this.postProcessingManager = new PostProcessingManager(this.scene, this.camera, this.renderer);
  }

  /**
   * 初始化EarthGroup的GUI控制面板
   */
  initEarthGroupGUI() {
    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建EarthGroup控制文件夹
      this.earthGroupFolder = this.gui.addFolder("🌍 地球组控制 (Earth Group)");
      this.earthGroupFolder.open();

      // 位置控制
      const positionFolder = this.earthGroupFolder.addFolder("📍 位置 (Position)");
      positionFolder
        .add(this.earthGroupParams.position, "x", -50, 50, 0.1)
        .name("X轴位置")
        .onChange((value) => {
          this.earthGroup.position.x = value;
          this.dispatchEvent("earthGroupPositionChanged", this.earthGroupParams.position);
        });

      positionFolder
        .add(this.earthGroupParams.position, "y", -50, 50, 0.1)
        .name("Y轴位置")
        .onChange((value) => {
          this.earthGroup.position.y = value;
          this.dispatchEvent("earthGroupPositionChanged", this.earthGroupParams.position);
        });

      positionFolder
        .add(this.earthGroupParams.position, "z", -50, 50, 0.1)
        .name("Z轴位置")
        .onChange((value) => {
          this.earthGroup.position.z = value;
          this.dispatchEvent("earthGroupPositionChanged", this.earthGroupParams.position);
        });

      // 旋转控制
      const rotationFolder = this.earthGroupFolder.addFolder("🔄 旋转 (Rotation)");
      rotationFolder
        .add(this.earthGroupParams.rotation, "x", -Math.PI, Math.PI, 0.01)
        .name("X轴旋转")
        .onChange((value) => {
          this.earthGroup.rotation.x = value;
          this.dispatchEvent("earthGroupRotationChanged", this.earthGroupParams.rotation);
        });

      rotationFolder
        .add(this.earthGroupParams.rotation, "y", -Math.PI, Math.PI, 0.01)
        .name("Y轴旋转")
        .onChange((value) => {
          this.earthGroup.rotation.y = value;
          this.dispatchEvent("earthGroupRotationChanged", this.earthGroupParams.rotation);
        });

      rotationFolder
        .add(this.earthGroupParams.rotation, "z", -Math.PI, Math.PI, 0.01)
        .name("Z轴旋转")
        .onChange((value) => {
          this.earthGroup.rotation.z = value;
          this.dispatchEvent("earthGroupRotationChanged", this.earthGroupParams.rotation);
        });

      // 缩放控制
      const scaleFolder = this.earthGroupFolder.addFolder("📏 缩放 (Scale)");
      scaleFolder
        .add(this.earthGroupParams.scale, "x", 0.1, 3, 0.01)
        .name("X轴缩放")
        .onChange((value) => {
          this.earthGroup.scale.x = value;
          this.dispatchEvent("earthGroupScaleChanged", this.earthGroupParams.scale);
        });

      scaleFolder
        .add(this.earthGroupParams.scale, "y", 0.1, 3, 0.01)
        .name("Y轴缩放")
        .onChange((value) => {
          this.earthGroup.scale.y = value;
          this.dispatchEvent("earthGroupScaleChanged", this.earthGroupParams.scale);
        });

      scaleFolder
        .add(this.earthGroupParams.scale, "z", 0.1, 3, 0.01)
        .name("Z轴缩放")
        .onChange((value) => {
          this.earthGroup.scale.z = value;
          this.dispatchEvent("earthGroupScaleChanged", this.earthGroupParams.scale);
        });

      // 统一缩放控制
      const uniformScale = { value: 1 };
      scaleFolder
        .add(uniformScale, "value", 0.1, 3, 0.01)
        .name("统一缩放")
        .onChange((value) => {
          this.earthGroup.scale.setScalar(value);
          this.earthGroupParams.scale.x = value;
          this.earthGroupParams.scale.y = value;
          this.earthGroupParams.scale.z = value;
          this.dispatchEvent("earthGroupScaleChanged", this.earthGroupParams.scale);
        });

      // 可见性控制
      this.earthGroupFolder
        .add(this.earthGroupParams, "visible")
        .name("显示/隐藏")
        .onChange((value) => {
          this.earthGroup.visible = value;
          this.dispatchEvent("earthGroupVisibilityChanged", { visible: value });
        });

      // 重置按钮
      const resetParams = {
        resetPosition: () => {
          this.earthGroupParams.position = { x: 0, y: 0, z: 0 };
          this.earthGroup.position.set(0, 0, 0);
          this.refreshGUI();
        },
        resetRotation: () => {
          this.earthGroupParams.rotation = { x: 0, y: 0, z: 0 };
          this.earthGroup.rotation.set(0, 0, 0);
          this.refreshGUI();
        },
        resetScale: () => {
          this.earthGroupParams.scale = { x: 1, y: 1, z: 1 };
          this.earthGroup.scale.set(1, 1, 1);
          this.refreshGUI();
        },
        resetAll: () => {
          this.earthGroupParams.position = { x: 0, y: 0, z: 0 };
          this.earthGroupParams.rotation = { x: 0, y: 0, z: 0 };
          this.earthGroupParams.scale = { x: 1, y: 1, z: 1 };
          this.earthGroup.position.set(0, 0, 0);
          this.earthGroup.rotation.set(0, 0, 0);
          this.earthGroup.scale.set(1, 1, 1);
          this.refreshGUI();
        },
      };

      this.earthGroupFolder.add(resetParams, "resetPosition").name("🔄 重置位置");
      this.earthGroupFolder.add(resetParams, "resetRotation").name("🔄 重置旋转");
      this.earthGroupFolder.add(resetParams, "resetScale").name("🔄 重置缩放");
      this.earthGroupFolder.add(resetParams, "resetAll").name("🔄 重置全部");

      // 地球动画控制
      const animationFolder = this.earthGroupFolder.addFolder("🎬 地球动画 (Earth Animation)");

      // 弧形高度控制
      animationFolder
        .add(this.earthAnimationParams, "arcHeight", -20, 20, 0.5)
        .name("弧形高度")
        .onChange((value) => {
          console.log("弧形高度已更改:", value);
        });

      // 中间点位置控制
      animationFolder
        .add(this.earthAnimationParams, "midPointPosition", 0, 1, 0.1)
        .name("中间点位置")
        .onChange((value) => {
          console.log("中间点位置已更改:", value);
        });

      // 位移延迟控制
      animationFolder
        .add(this.earthAnimationParams, "positionDelay", 0, 2000, 100)
        .name("位移延迟(ms)")
        .onChange((value) => {
          console.log("位移延迟已更改:", value + "ms");
        });

      // 动画持续时间控制
      animationFolder
        .add(this.earthAnimationParams, "duration", 1000, 10000, 100)
        .name("动画时长(ms)")
        .onChange((value) => {
          console.log("动画时长已更改:", value);
        });

      // 圆环动画触发进度控制
      animationFolder
        .add(this.earthAnimationParams, "ringAnimationTriggerProgress", 0, 1, 0.05)
        .name("圆环触发进度")
        .onChange((value) => {
          console.log("圆环动画触发进度已更改:", value.toFixed(2), `(${(value * 100).toFixed(0)}%)`);
        });

      // 飞线动画触发进度控制
      animationFolder
        .add(this.earthAnimationParams, "flightLineAnimationTriggerProgress", 0, 1, 0.05)
        .name("飞线触发进度")
        .onChange((value) => {
          console.log("飞线动画触发进度已更改:", value.toFixed(2), `(${(value * 100).toFixed(0)}%)`);
        });

      // 动画控制按钮
      const animationControls = {
        startAnimation: () => {
          console.log(
            "启动地球动画 - 弧形高度:",
            this.earthAnimationParams.arcHeight,
            "中间点位置:",
            this.earthAnimationParams.midPointPosition,
            "位移延迟:",
            this.earthAnimationParams.positionDelay + "ms"
          );
          this.startEarthAnimation();
        },
        stopAnimation: () => {
          if (this.cameraAnimationController) {
            this.cameraAnimationController.stopAnimation();
            console.log("地球动画已停止");
          }
        },
      };

      animationFolder.add(animationControls, "startAnimation").name("▶️ 开始动画");
      animationFolder.add(animationControls, "stopAnimation").name("⏹️ 停止动画");

      // 圆环动画控制
      const ringAnimationControls = {
        showRings: () => {
          console.log("手动显示圆环（自定义时长）");
          this.startRingScaleAnimation();
        },
        showRingsSync: () => {
          if (this.planetRings) {
            console.log("同步显示圆环");
            this.planetRings.animateScaleTo(1.0, 1000, () => {
              console.log("圆环同步显示完成");
            });
          }
        },
        hideRings: () => {
          if (this.planetRings) {
            console.log("隐藏圆环");
            this.planetRings.animateRingsWithCustomTiming(
              [
                { targetScale: 0, duration: 600, delay: 0 }, // 第一个圆环快速隐藏
                { targetScale: 0, duration: 900, delay: 200 }, // 第二个圆环慢速隐藏
              ],
              () => {
                console.log("圆环已隐藏");
              }
            );
          }
        },
        resetRings: () => {
          if (this.planetRings) {
            console.log("重置圆环到初始状态");
            this.planetRings.setInitialScale(0);
          }
        },
      };

      animationFolder.add(ringAnimationControls, "showRings").name("🪐 显示圆环(异步)");
      animationFolder.add(ringAnimationControls, "showRingsSync").name("⚡ 显示圆环(同步)");
      animationFolder.add(ringAnimationControls, "hideRings").name("🫥 隐藏圆环");
      animationFolder.add(ringAnimationControls, "resetRings").name("🔄 重置圆环");
    } catch (error) {
      console.warn("EarthGroup GUI setup warning:", error);
    }
  }

  /**
   * 刷新GUI显示
   */
  refreshGUI() {
    if (this.earthGroupFolder) {
      // 遍历所有控制器并更新显示
      this.earthGroupFolder.controllersRecursive().forEach((controller) => {
        controller.updateDisplay();
      });
    }
  }

  /**
   * 同步GUI参数与地球组的实际状态
   */
  syncGUIWithEarthGroup() {
    if (this.earthGroup) {
      // 同步位置参数
      this.earthGroupParams.position.x = this.earthGroup.position.x;
      this.earthGroupParams.position.y = this.earthGroup.position.y;
      this.earthGroupParams.position.z = this.earthGroup.position.z;

      // 同步旋转参数
      this.earthGroupParams.rotation.x = this.earthGroup.rotation.x;
      this.earthGroupParams.rotation.y = this.earthGroup.rotation.y;
      this.earthGroupParams.rotation.z = this.earthGroup.rotation.z;

      // 同步缩放参数
      this.earthGroupParams.scale.x = this.earthGroup.scale.x;
      this.earthGroupParams.scale.y = this.earthGroup.scale.y;
      this.earthGroupParams.scale.z = this.earthGroup.scale.z;

      // 刷新GUI显示
      this.refreshGUI();

      console.log("GUI参数已与地球组状态同步:", {
        position: this.earthGroupParams.position,
        rotation: this.earthGroupParams.rotation,
        scale: this.earthGroupParams.scale,
      });
    }
  }

  setupEventListeners() {
    window.addEventListener("resize", this.onWindowResize.bind(this));

    // 监听地球动画完成事件，同步GUI参数
    this.addEventListener("earthAnimationComplete", () => {
      this.syncGUIWithEarthGroup();
    });
  }

  onWindowResize() {
    if (this.isDestroyed) return;

    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);

    // 更新后处理管理器
    if (this.postProcessingManager) {
      this.postProcessingManager.onWindowResize(width, height);
    }
  }

  animate() {
    if (this.isDestroyed) return;

    this.animationId = requestAnimationFrame(this.animate.bind(this));

    // Update lights first to update light position
    if (this.lights) this.lights.update();

    // Get light position from scene userData
    const lightPos = this.scene.userData?.lightPos;

    // Update other components with light position
    if (this.clouds) this.clouds.update();
    if (this.earthNight) this.earthNight.update(lightPos);
    if (this.honeycombGrid) this.honeycombGrid.update();
    if (this.starField) this.starField.update();
    if (this.orbitingStars) this.orbitingStars.update();
    if (this.planetRings) this.planetRings.update();

    // Update controls
    this.controls.update();

    // Render with post-processing
    if (this.postProcessingManager) {
      this.postProcessingManager.render();
    } else {
      this.renderer.render(this.scene, this.camera);
    }
  }

  destroy() {
    this.isDestroyed = true;

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    // Cleanup GUI
    if (this.earthGroupFolder) {
      this.earthGroupFolder.destroy();
      this.earthGroupFolder = null;
    }

    // Cleanup components
    if (this.earthDay) this.earthDay.destroy();
    if (this.clouds) this.clouds.destroy();
    if (this.earthNight) this.earthNight.destroy();
    if (this.halo) this.halo.destroy();
    if (this.lights) this.lights.destroy();
    if (this.honeycombGrid) this.honeycombGrid.destroy();
    if (this.spherePoints) this.spherePoints.destroy();
    if (this.starField) this.starField.destroy();
    if (this.orbitingStars) this.orbitingStars.destroy();
    if (this.planetRings) this.planetRings.destroy();

    if (this.cameraAnimationController) this.cameraAnimationController.destroy();
    if (this.postProcessingManager) this.postProcessingManager.destroy();

    // Cleanup Three.js objects
    if (this.controls) this.controls.dispose();
    if (this.renderer) {
      this.renderer.dispose();
      if (this.container.contains(this.renderer.domElement)) {
        this.container.removeChild(this.renderer.domElement);
      }
    }

    // Remove event listeners
    window.removeEventListener("resize", this.onWindowResize.bind(this));

    // Clear event listeners
    this.eventListeners.clear();

    // Clear singleton instance
    Scene.instance = null;
  }
}

// Export both named and default exports for flexibility
export { Scene };
export default Scene;
