<template>
  <div class="Left" :style="animationStyles">
    <transition name="slide-right">
      <div v-if="uiAnimationTriggered" class="LeftHead1" :style="{ animationDelay: animationDelays[0] }">
        <div class="LeftHead1Text">互联网出口流量</div>
      </div>
    </transition>
    <transition name="slide-right">
      <div v-if="uiAnimationTriggered" class="LeftHead1Body1" :style="{ animationDelay: animationDelays[1] }"><Qx2 /></div>
    </transition>

    <transition name="slide-right">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead2" :style="{ animationDelay: animationDelays[2] }">
        <div class="LeftHead1Text">近两天攻击对比趋势</div>
      </div>
    </transition>
    <!-- <div class="LeftHead2Body"></div> -->
    <transition name="slide-right">
      <div v-if="uiAnimationTriggered" class="LeftHead2Body" :style="{ animationDelay: animationDelays[3] }"><Qx3 /></div>
    </transition>

    <transition name="slide-right">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead3" :style="{ animationDelay: animationDelays[4] }">
        <div class="LeftHead1Text">近期攻击态势</div>
      </div>
    </transition>
    <transition name="slide-right">
      <div v-if="uiAnimationTriggered" class="LeftHead3Body" :style="{ animationDelay: animationDelays[5] }">
        <AttackData3DFlip class="attack-3d-flip-container" />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, inject } from "vue";

import Qx2 from "./Qx2.vue";
import Qx3 from "./Qx3.vue";
import AttackData3DFlip from "../components/AttackData3DFlip.vue";

// 注入UI动画触发状态
const uiAnimationTriggered = inject("uiAnimationTriggered", ref(false));

// 动画总时长配置 (秒)
const totalAnimationDuration = ref(2);

// 计算单个动画持续时间 (总时长的1/3)
const singleAnimationDuration = computed(() => {
  return totalAnimationDuration.value / 3;
});

// 计算动画延迟间隔 (总时长除以元素数量)
const delayInterval = computed(() => {
  return totalAnimationDuration.value / 6; // 6个元素
});

// 计算各个元素的动画延迟
const animationDelays = computed(() => {
  const delays = [];
  for (let i = 0; i < 6; i++) {
    delays.push((i * delayInterval.value).toFixed(2) + "s");
  }
  return delays;
});

// CSS 变量，用于动态设置动画时长
const animationStyles = computed(() => {
  return {
    "--animation-duration": singleAnimationDuration.value + "s",
    "--total-duration": totalAnimationDuration.value + "s",
  };
});
</script>

<style lang="less" scoped>
/* 从右到左滑动动画样式 */
.slide-right-enter-active {
  animation: slideInFromRight var(--animation-duration, 1s) ease-out forwards;
}

.slide-right-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 为每个元素添加初始状态和动画延迟 */
.LeftHead1,
.LeftHead1Body1,
.LeftHead2,
.LeftHead2Body,
.LeftHead3,
.LeftHead3Body {
  animation: slideInFromRight var(--animation-duration, 1s) ease-out forwards;
  transform: translateX(100%);
  opacity: 0;
}

.Left {
  width: 100%;
  height: 100%;
  position: relative;

  .LeftHead1 {
    position: absolute;
    top: 15px;
    width: 380px;
    height: 35px;
    background-image: url("../../public/img/cardHead.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    right: 154px;
    .LeftHead1Text {
      font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
      font-weight: bold;
      font-size: 18px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      margin-left: 25px;
    }
  }
  .LeftHead2 {
    position: absolute;
    right: 107px;
    top: 231px;
  }
  .LeftHead3 {
    position: absolute;
    right: 107px;
    top: 450px;
  }
  .LeftHead4 {
    position: absolute;
    left: 92.25px;
    top: 679px;
  }
  .LeftHead1Body1 {
    width: 385px;
    height: 172px;

    position: absolute;
    top: 38px;
    right: 152px;
  }
  .LeftHead1Body12 {
    position: absolute;
    left: 106px;
    top: 150px;
  }
  .LeftHead2Body {
    position: absolute;
    right: 100px;
    top: 258px;
    width: 370px;
    height: 172px;
  }
  .LeftHead3Body {
    position: absolute;
    right: 70px;
    top: 490px;
    width: 480px;
    height: 413px;
    pointer-events: all;
  }

  .attack-3d-flip-container {
    width: 100%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
}
</style>
