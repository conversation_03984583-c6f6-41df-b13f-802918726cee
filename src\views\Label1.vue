<template>
  <div class="Label1">
    <div class="Label1Left" :class="`Label1Left${iconId}`">
      <div class="Label1LeftChi" :class="`Label1LeftChi${iconId}`"></div>
    </div>
    <div class="Label1Right">
      <div class="Label1Right1">{{ label1Right1Text }}</div>
      <div class="Label1Right2">{{ label1Right2Text }}</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  iconId: {
    type: Number,
    default: 1,
  },
  label1Right1Text: {
    type: String,
    default: "总警告数",
  },
  label1Right2Text: {
    type: String,
    default: "92.94%",
  },
});
</script>

<style lang="less" scoped>
.Label1 {
  width: 100%;
  height: 100%;

  display: flex;
  align-items: center;

  .Label1Left {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;

    background-image: url("../../public/img/q.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: rotate 3s linear infinite;
  }
  .Label1LeftChi {
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: counter-rotate 3s linear infinite;
  }
  .Label1LeftChi1 {
    background-image: url("../../public/img/1.png");
  }
  .Label1LeftChi2 {
    background-image: url("../../public/img/2.png");
  }
  .Label1LeftChi3 {
    background-image: url("../../public/img/3.png");
  }
  .Label1LeftChi4 {
    background-image: url("../../public/img/4.png");
  }
  .Label1Right {
    margin-left: 20px;
    .Label1Right1 {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #b8b8b8;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
    .Label1Right2 {
      font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
      font-weight: bold;
      font-size: 20px;
      color: #ffffff;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes counter-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
</style>
